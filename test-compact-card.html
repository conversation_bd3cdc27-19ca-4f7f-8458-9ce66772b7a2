<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧凑型商品分析卡片测试</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .comparison-section {
            margin-bottom: 40px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }
        .cards-comparison {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        @media (min-width: 1024px) {
            .cards-comparison {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--text-primary); margin-bottom: 40px;">
            商品分析卡片对比测试
        </h1>
        
        <div class="comparison-section">
            <div class="section-title">新版紧凑型商品分析卡片</div>
            <div class="product-analysis-card-compact">
                <div class="product-analysis-header-compact">
                    <h4><i class="ri-shopping-bag-line"></i> 商品信息分析结果</h4>
                </div>
                <div class="product-analysis-content-compact">
                    <div class="product-info-grid">
                        <div class="product-info-row">
                            <div class="info-item-compact">
                                <label class="info-label-compact">商品名称</label>
                                <input type="text" class="editable-field-compact" value="Earbud 智能翻译耳机" disabled>
                            </div>
                            <div class="info-item-compact">
                                <label class="info-label-compact">价格区间</label>
                                <input type="text" class="editable-field-compact" value="50-100元" disabled>
                            </div>
                        </div>
                        <div class="product-info-row">
                            <div class="info-item-compact full-width">
                                <label class="info-label-compact">核心特性</label>
                                <input type="text" class="editable-field-compact" value="多语言实时翻译, AI语音助手, 高清音质, 降噪技术, 长续航, 防水设计" disabled>
                            </div>
                        </div>
                        <div class="product-info-row">
                            <div class="info-item-compact">
                                <label class="info-label-compact">目标受众</label>
                                <input type="text" class="editable-field-compact" value="商务人士, 旅行者, 科技爱好者" disabled>
                            </div>
                            <div class="info-item-compact">
                                <label class="info-label-compact">特征标签</label>
                                <input type="text" class="editable-field-compact" value="智能翻译, 语音识别, 降噪技术" disabled>
                            </div>
                        </div>
                        <div class="product-info-row">
                            <div class="info-item-compact">
                                <label class="info-label-compact">受众标签</label>
                                <input type="text" class="editable-field-compact" value="商务精英, 国际旅行者, 科技发烧友" disabled>
                            </div>
                            <div class="info-item-compact">
                                <label class="info-label-compact">场景标签</label>
                                <input type="text" class="editable-field-compact" value="商务会议, 国际旅行, 语言学习" disabled>
                            </div>
                        </div>
                        <div class="product-info-row">
                            <div class="info-item-compact full-width">
                                <label class="info-label-compact">核心内容方向</label>
                                <input type="text" class="editable-field-compact" value="AI技术应用, 跨语言沟通, 智能穿戴设备, 旅行科技装备" disabled>
                            </div>
                        </div>
                        <div class="product-info-row">
                            <div class="info-item-compact">
                                <label class="info-label-compact">综合人设/风格</label>
                                <input type="text" class="editable-field-compact" value="科技前沿, 国际化视野, 高效便捷" disabled>
                            </div>
                            <div class="info-item-compact">
                                <label class="info-label-compact">主要受众画像</label>
                                <input type="text" class="editable-field-compact" value="25-45岁职场人士, 年收入10万+" disabled>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-analysis-actions-compact">
                    <button class="edit-product-btn-compact" onclick="toggleEdit()">
                        <i class="ri-edit-line"></i> 修改信息
                    </button>
                    <button class="confirm-product-btn-compact primary-btn-compact">
                        <i class="ri-file-list-line"></i> 开始提取特征
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleEdit() {
            const fields = document.querySelectorAll('.editable-field-compact');
            const btn = document.querySelector('.edit-product-btn-compact');
            
            fields.forEach(field => {
                field.disabled = !field.disabled;
            });
            
            if (fields[0].disabled) {
                btn.innerHTML = '<i class="ri-edit-line"></i> 修改信息';
            } else {
                btn.innerHTML = '<i class="ri-save-line"></i> 保存修改';
            }
        }
    </script>
</body>
</html>
